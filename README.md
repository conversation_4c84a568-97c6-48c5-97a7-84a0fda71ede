# Chat to Design 后端服务

这是 Chat to Design 应用的后端服务，使用 Hono 构建并运行在 Cloudflare Workers 上。它提供了多种功能，包括聊天功能、图像生成、Notion 集成和文件上传。

## 目录

- [Chat to Design 后端服务](#chat-to-design-后端服务)
  - [目录](#目录)
  - [技术栈](#技术栈)
  - [项目结构](#项目结构)
  - [前置要求](#前置要求)
  - [安装设置](#安装设置)
  - [本地开发](#本地开发)
  - [生产环境构建](#生产环境构建)
  - [部署](#部署)
  - [运行测试](#运行测试)
  - [环境变量和密钥](#环境变量和密钥)
  - [主要功能](#主要功能)
  - [API 端点](#api-端点)

## 技术栈

- **运行时/框架**: Node.js, Hono, Cloudflare Workers
- **语言**: TypeScript
- **包管理器**: pnpm
- **API 和后端服务**:
  - `@google/generative-ai`: Google Generative AI
  - `@notionhq/client`: Notion API
  - `firebase-rest-firestore`: Firebase Firestore (如果仍在使用，否则请移除)
  - `@upstash/ratelimit` & `@upstash/redis`: Upstash 用于速率限制和 Redis
- **图像处理**: `sharp`
- **校验**: `zod`, `@hono/zod-validator`
- **依赖注入**: `tsyringe`, `reflect-metadata`
- **构建工具**: Vite, esbuild
- **测试**: Vitest, tsx
- **部署**: Cloudflare Wrangler

## 项目结构

```
chat-to-design-backend/
├── .github/            # GitHub Actions 工作流
├── .wrangler/          # Wrangler 本地开发状态
├── public/             # 静态资源
│   └── static/
├── src/                # 源代码
│   ├── features/       # 特定功能模块
│   │   ├── chat/
│   │   ├── env/
│   │   ├── file-upload/
│   │   ├── image-generation/
│   │   ├── message-process/
│   │   ├── notion/
│   │   ├── usecases/
│   │   └── user/
│   ├── infrastructure/ #核心基础架构 (AI, 缓存, 数据库等)
│   ├── lib/            #共享库和工具
│   └── types/          # TypeScript 类型定义
├── .dev.vars           # Wrangler 本地开发环境变量
├── .env                # 通用环境变量 (例如，用于本地 Node 脚本)
├── .env.example        # .env 文件示例
├── .gitignore
├── Dockerfile          # Docker 配置 (如果用于其他目的)
├── fly.toml            # Fly.io 配置 (如果用于部署)
├── package.json        # 项目依赖和脚本
├── pnpm-lock.yaml
├── README.md
├── secrets.json        # Cloudflare Workers 密钥模板 (不要提交实际密钥)
├── tsconfig.json       # TypeScript 配置
├── vite.config.ts      # Vite 配置
├── vitest.config.ts    # Vitest 配置
└── wrangler.jsonc      # Wrangler 配置
```

## 前置要求

1.  **Node.js**: 版本 18.x 或更高。(建议使用版本管理器如 `nvm`)
2.  **pnpm**: 通过 `npm install -g pnpm` 安装或查看 [pnpm 安装指南](https://pnpm.io/installation)。
3.  **Cloudflare 账户**: 部署到 Cloudflare Workers 时需要。
4.  **Wrangler CLI**: 安装并配置 Cloudflare Wrangler CLI。
    ```bash
    npm install -g wrangler
    wrangler login
    ```
5.  **外部服务**:
    - 为所使用的任何外部服务（例如 Google AI, Notion, Upstash Redis, Firebase）设置账户并获取 API 密钥/凭证。

## 安装设置

1.  **克隆仓库**:

    ```bash
    git clone <repository-url>
    cd chat-to-design-backend
    ```

2.  **安装依赖**:

    ```bash
    pnpm install
    ```

3.  **配置环境变量**:
    - **针对 Cloudflare Workers 密钥 (生产/预览环境)**:
      - 复制 `secrets.json.example` (如果存在) 或根据所需服务的密钥 (如 Google AI, Notion, Upstash 等) 创建 `secrets.json`。
      - 用您的实际密钥值填充 `secrets.json`。
      - **重要**: 如果 `secrets.json` 包含真实密钥，则不应将其提交到版本控制系统。它用作 `wrangler secret bulk put` 的源文件。
      - 将密钥上传到 Cloudflare:
        ```bash
        pnpm run secrets:push
        ```
    - **针对使用 Wrangler (`wrangler dev`) 的本地开发**:
      - 在根目录中创建一个 `.dev.vars` 文件。`wrangler dev` 会自动加载此文件。
      - 在此处以 `KEY=VALUE` 格式添加您的开发环境变量。例如:
        ```
        GOOGLE_API_KEY="your_google_api_key"
        NOTION_API_KEY="your_notion_api_key"
        UPSTASH_REDIS_REST_URL="your_upstash_url"
        UPSTASH_REDIS_REST_TOKEN="your_upstash_token"
        # ... 其他变量
        ```
    - **针对其他本地 Node.js 脚本或非 Wrangler 环境**:
      - 将 `.env.example` 复制为 `.env`。
      - 使用您的本地配置值更新 `.env`。如果不是通过 Wrangler 运行，某些测试脚本或自定义 Node.js 脚本会加载此文件。

## 本地开发

使用 Wrangler 启动开发服务器。这通常会在本地运行服务，通常是 `http://localhost:8787` (请检查 Wrangler 输出以获取确切端口)。HMR (热模块替换)应该处于活动状态。

```bash
pnpm run dev
```

或者，对于更接近 Cloudflare 环境的本地预览（但重新加载速度可能较慢）：

```bash
pnpm run preview
```

## 生产环境构建

编译 TypeScript 代码并使用 Vite 打包应用程序 (在 `wrangler.jsonc` 中配置，或者如果 Wrangler 构建过程直接使用 `vite.config.ts` )。

```bash
pnpm run build
```

该命令通常在部署过程中由 Wrangler 调用。

## 部署

将服务部署到 Cloudflare Workers:

```bash
pnpm run deploy
```

此命令将根据 `wrangler.jsonc` 配置构建和部署您的应用程序。请确保您在 Cloudflare 仪表板中的密钥是最新的 (通过 `pnpm run secrets:push` 推送)。

## 运行测试

项目在 `package.json` 中定义了几个测试脚本:

```bash
# 运行特定的测试套件 (示例)
pnpm run test:service
pnpm run test:api
pnpm run test:batch
pnpm run test:image
pnpm run test:chat-service
pnpm run test:chat-api
pnpm run test:message-process
pnpm run test:storage

# 使用 Vitest 运行所有测试 (如果为此配置)
# pnpm vitest
```

有关可用测试命令及其特定用途的完整列表，请参阅 `package.json`。如果测试依赖于某些服务（如本地数据库或模拟服务器），请确保它们正在运行，或者测试已正确模拟。

## 环境变量和密钥

- **`.dev.vars`**: 由 `wrangler dev` 用于本地开发。存储非敏感配置，如果它们不敏感或是占位符，则可以提交。对于实际密钥，请使用您的 shell 或 CI/CD 系统提供的环境变量。
- **`secrets.json`**: 此文件 **不是** `.dev.vars`。它是一个本地 JSON 文件，`wrangler secret bulk put secrets.json` 使用它将密钥上传到您在 Cloudflare 仪表板中的 Worker。**如果 `secrets.json` 包含真实的生产密钥，请勿提交它。** 请使用 `secrets.json.example` 作为模板。
- **Cloudflare 仪表板密钥**:直接在 Cloudflare Workers 仪表板中管理的密钥。这些密钥在运行时注入到您的 Worker 中。使用 `wrangler secret put KEY` 或 `wrangler secret bulk put secrets.json` 来管理这些密钥。
- **`.env`**: 用于可能不通过 `wrangler dev` 运行的通用 Node.js 脚本或本地测试的标准 dotenv 文件。此处的值通常用于本地设置，可能与 `.dev.vars` 重叠，但服务于不同的执行上下文。

## 主要功能

根据 `src/features/` 目录结构，后端可能支持:

- **聊天 (Chat)**: 实时或异步聊天功能。
- **环境管理 (Environment Management)**: 处理特定于环境的配置。
- **文件上传 (File Upload)**: 用户上传文件的功能。
- **图像生成 (Image Generation)**: 与 AI 集成以生成图像。
- **消息处理 (Message Processing)**: 处理和加工消息的逻辑。
- **Notion 集成 (Notion Integration)**: 与 Notion 工作区交互 (例如，读取/写入数据)。
- **用例 (Usecases)**: 特定的业务逻辑或应用程序用例。
- **用户管理 (User Management)**: 用户身份验证、配置文件或相关功能。

## API 端点

(此处应添加有关特定 API 端点的信息。可以手动记录，或者如果 Hono 设置包含 Swagger/OpenAPI 之类的工具，则可以使用这些工具生成。)

示例:

- `POST /api/chat` - 发送新的聊天消息。
- `GET /api/images/generate` - 根据提示生成图像。
- `POST /api/files/upload` - 上传文件。

有关可用 API 端点的完整列表，请参阅 `src/features/**/endpoints/` 中的路由定义。

```
open http://localhost:3000
```
